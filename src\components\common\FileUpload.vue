<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :class="uploadClass"
      :action="uploadUrl"
      :headers="headers"
      :accept="accept"
      :multiple="multiple"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :disabled="disabled"
      drag
    >
      <!-- 已有文件列表 -->
      <template v-if="fileList.length > 0">
        <div class="file-list">
          <div
            v-for="(file, index) in fileList"
            :key="file.id"
            class="file-item"
          >
            <!-- 文件预览 -->
            <div class="file-preview">
              <img
                v-if="isImage(file.type)"
                :src="file.previewUrl"
                alt="预览图"
                @error="handleImageError"
              />
              <div v-else-if="file.type === 'pdf'" class="pdf-preview">
                <el-icon class="pdf-icon"><Document /></el-icon>
                <span>PDF</span>
              </div>
              <div v-else class="file-preview-default">
                <el-icon><Document /></el-icon>
                <span>{{ file.name }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="file-operations">
              <el-button
                type="primary"
                :icon="ZoomIn"
                circle
                size="small"
                @click="handlePreview(index)"
              />
              <el-button
                v-if="!disabled"
                type="danger"
                :icon="Delete"
                circle
                size="small"
                @click="handleDelete(index)"
              />
            </div>

            <!-- 上传进度 -->
            <div v-if="file.uploading" class="upload-progress">
              <el-progress :percentage="file.progress" :show-text="false" />
            </div>
          </div>

          <!-- 添加更多文件按钮 -->
          <div
            v-if="!disabled && (!maxCount || fileList.length < maxCount)"
            class="upload-btn"
          >
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击或拖拽上传</div>
          </div>
        </div>
      </template>

      <!-- 空状态 -->
      <template v-else>
        <div v-if="disabled" class="empty-state">
          <el-icon><Document /></el-icon>
          <div class="upload-text">暂无文件</div>
        </div>
        <div v-else class="upload-btn">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击或拖拽文件到此处上传</div>
        </div>
      </template>
    </el-upload>

    <!-- 提示信息 -->
    <div v-if="!disabled" class="upload-tips">
      仅支持 {{ acceptText }} 文件格式，且文件大小不能超过 {{ maxFileSize }}MB
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="文件预览"
      width="80%"
      :before-close="handleClosePreview"
    >
      <div class="preview-content">
        <img
          v-if="currentPreviewFile && isImage(currentPreviewFile.type)"
          :src="currentPreviewFile.url"
          alt="预览图"
          style="max-width: 100%; max-height: 70vh"
        />
        <iframe
          v-else-if="currentPreviewFile && currentPreviewFile.type === 'pdf'"
          :src="currentPreviewFile.url"
          style="width: 100%; height: 70vh; border: none"
        />
        <div v-else class="unsupported-preview">
          <el-icon size="48"><Document /></el-icon>
          <p>该文件类型不支持预览</p>
          <el-button type="primary" @click="downloadFile(currentPreviewFile)">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, ZoomIn, Document } from '@element-plus/icons-vue'

defineOptions({
  name: 'FileUpload',
})

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: '.jpg,.jpeg,.png,.pdf',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  maxFileSize: {
    type: Number,
    default: 10,
  },
  maxCount: {
    type: Number,
    default: 0,
  },
  uploadUrl: {
    type: String,
    default: '/api/upload',
  },
  headers: {
    type: Object,
    default: () => ({}),
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// Refs
const uploadRef = ref()
const fileList = ref([])
const previewVisible = ref(false)
const currentPreviewFile = ref(null)

// Computed
const uploadClass = computed(() => ({
  'file-upload-container': true,
  'is-disabled': props.disabled,
}))

const acceptText = computed(() => {
  return props.accept.replace(/\./g, '').toUpperCase()
})

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      const urls = newVal.split(',').filter((url) => url.trim())
      fileList.value = urls.map((url, index) => ({
        id: `file_${Date.now()}_${index}`,
        name: getFileNameFromUrl(url),
        url: url,
        previewUrl: getPreviewUrl(url),
        type: getFileType(url),
        uploading: false,
        progress: 100,
      }))
    } else {
      fileList.value = []
    }
  },
  { immediate: true },
)

// Methods
const getFileNameFromUrl = (url) => {
  return url.split('/').pop() || 'unknown'
}

const getFileType = (url) => {
  const extension = url.split('.').pop()?.toLowerCase()
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image'
  } else if (extension === 'pdf') {
    return 'pdf'
  }
  return 'other'
}

const getPreviewUrl = (url) => {
  // 这里可以根据实际需求处理预览URL
  return url
}

const isImage = (type) => {
  return type === 'image'
}

const beforeUpload = (file) => {
  // 检查文件类型
  const acceptTypes = props.accept.split(',').map((type) => type.trim())
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

  if (!acceptTypes.includes(fileExtension)) {
    ElMessage.error(`只能上传 ${props.acceptText} 格式的文件!`)
    return false
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxFileSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxFileSize}MB!`)
    return false
  }

  // 检查文件数量
  if (props.maxCount && fileList.value.length >= props.maxCount) {
    ElMessage.error(`最多只能上传 ${props.maxCount} 个文件!`)
    return false
  }

  // 添加到文件列表（显示上传进度）
  const fileItem = {
    id: `file_${Date.now()}_${Math.random()}`,
    name: file.name,
    url: '',
    previewUrl: '',
    type: getFileType(file.name),
    uploading: true,
    progress: 0,
  }
  fileList.value.push(fileItem)

  return true
}

const handleSuccess = (response, file) => {
  // 找到对应的文件项并更新
  const fileItem = fileList.value.find(
    (item) => item.uploading && item.name === file.name,
  )
  if (fileItem) {
    fileItem.uploading = false
    fileItem.progress = 100
    fileItem.url = response.url || response.data?.url || ''
    fileItem.previewUrl = getPreviewUrl(fileItem.url)
  }

  updateModelValue()
  ElMessage.success('文件上传成功!')
}

const handleError = (error, file) => {
  // 移除上传失败的文件
  const index = fileList.value.findIndex(
    (item) => item.uploading && item.name === file.name,
  )
  if (index > -1) {
    fileList.value.splice(index, 1)
  }

  ElMessage.error('文件上传失败!')
}

const handlePreview = (index) => {
  currentPreviewFile.value = fileList.value[index]
  previewVisible.value = true
}

const handleDelete = (index) => {
  fileList.value.splice(index, 1)
  updateModelValue()
}

const handleClosePreview = () => {
  previewVisible.value = false
  currentPreviewFile.value = null
}

const handleImageError = (event) => {
  // 图片加载失败时的处理
  event.target.style.display = 'none'
}

const downloadFile = (file) => {
  if (file && file.url) {
    window.open(file.url, '_blank')
  }
}

const updateModelValue = () => {
  const urls = fileList.value
    .filter((file) => !file.uploading && file.url)
    .map((file) => file.url)
    .join(',')

  emit('update:modelValue', urls)
  emit('change', urls)
}
</script>

<style lang="scss" scoped>
.file-upload {
  .file-upload-container {
    :deep(.el-upload-dragger) {
      padding: 20px;
      border: 2px dashed #dcdfe6;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.2s;

      &:hover {
        border-color: #409eff;
      }
    }

    &.is-disabled {
      :deep(.el-upload-dragger) {
        cursor: not-allowed;
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #c0c4cc;
      }
    }
  }

  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    min-height: 120px;
    align-items: flex-start;
  }

  .file-item {
    position: relative;
    width: 120px;
    height: 100px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;

    .file-preview {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .pdf-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #f56c6c;

        .pdf-icon {
          font-size: 32px;
          margin-bottom: 4px;
        }

        span {
          font-size: 12px;
        }
      }

      .file-preview-default {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #909399;
        padding: 8px;
        text-align: center;

        .el-icon {
          font-size: 24px;
          margin-bottom: 4px;
        }

        span {
          font-size: 10px;
          word-break: break-all;
        }
      }
    }

    .file-operations {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover .file-operations {
      opacity: 1;
    }

    .upload-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 4px;
      background: rgba(255, 255, 255, 0.9);
    }
  }

  .upload-btn {
    width: 120px;
    height: 100px;
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;
    cursor: pointer;
    transition: border-color 0.2s;

    &:hover {
      border-color: #409eff;
      color: #409eff;
    }

    .upload-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 12px;
      text-align: center;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;
    padding: 40px;

    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 14px;
    }
  }

  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
  }

  .preview-content {
    text-align: center;

    .unsupported-preview {
      padding: 40px;
      color: #909399;

      .el-icon {
        margin-bottom: 16px;
      }

      p {
        margin: 16px 0;
        font-size: 14px;
      }
    }
  }
}
</style>

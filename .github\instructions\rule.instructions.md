---
applyTo: '**'
---
# 项目编码规范与指南

本文档提供项目上下文和编码指南，所有开发者和AI助手在生成代码、回答问题或审查变更时都应遵循这些规则。

## 组件使用规范

### 1. Element Plus组件优先原则

- **优先使用Element Plus组件库**中提供的组件，只有在组件库中不存在相应功能的组件时才自行开发
- 搜索功能应使用Element Plus的`el-input`组件结合`prefix-icon`或`suffix-icon`属性
- 表单元素（输入框、选择器、日期选择器等）应使用对应的Element Plus组件
- 弹窗、抽屉、消息提示等交互组件必须使用Element Plus提供的组件
- 使用Element Plus组件时，优先考虑按需导入以减小打包体积

**示例（推荐）：**
```vue
<template>
  <el-input
    v-model="searchText"
    placeholder="请输入搜索内容"
    :prefix-icon="Search"
    @input="handleSearch"
  />
</template>

<script setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

const searchText = ref('')
const handleSearch = () => {
  // 处理搜索逻辑
}
</script>
```

**示例（避免）：**
```vue
<template>
  <div class="search-container">
    <input 
      v-model="searchText" 
      placeholder="请输入搜索内容"
      @input="handleSearch"
    />
    <span class="search-icon">🔍</span>
  </div>
</template>
```

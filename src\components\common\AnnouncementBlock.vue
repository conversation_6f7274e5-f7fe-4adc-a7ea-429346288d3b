<template>
  <div class="announcement-section">
    <div class="section-header">
      <h3 class="section-title">{{ title }}</h3>
      <div class="flex flex-center gap-2">
        <el-link
          type="primary"
          class="more-link text-gray-6"
          @click="handleMoreClick"
          >更多</el-link
        >

        <el-icon size="14px" class="text-gray-5"><ArrowRight /></el-icon>
      </div>
    </div>
    <div class="announcement-list">
      <div
        v-for="item in announcements"
        :key="item.id"
        class="announcement-item"
      >
        <el-row align="middle">
          <el-col :span="14">
            <div class="announcement-title font-semibold text-gray-9">
              {{ item.title }}
            </div>
          </el-col>

          <!-- 固定布局：第一个时间字段 -->
          <el-col :span="3">
            <div v-if="timeFields[0]" class="time-info">
              <div class="time-label">{{ timeFields[0].label }}:</div>
              <div class="time-value">{{ item[timeFields[0].key] }}</div>
            </div>
          </el-col>

          <!-- 固定布局：第二个时间字段 -->
          <el-col :span="3">
            <div v-if="timeFields[1]" class="time-info">
              <div class="time-label">{{ timeFields[1].label }}:</div>
              <div class="time-value">{{ item[timeFields[1].key] }}</div>
            </div>
            <div
              v-else-if="timeFields[0] && timeFields.length === 1"
              class="time-info"
            >
              <div class="time-label">{{ timeFields[0].label }}:</div>
              <div class="time-value">{{ item[timeFields[0].key] }}</div>
            </div>
          </el-col>

          <!-- 状态字段（可选） -->
          <el-col v-if="showStatus" :span="3">
            <div class="status-info">
              <div class="status-label">项目状态:</div>
              <el-tag
                :type="item.status === '已发布者' ? 'primary' : 'warning'"
                size="small"
              >
                {{ item.status }}
              </el-tag>
            </div>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="1" :offset="statusOffset">
            <el-button type="primary" link @click="handleDetailClick(item)"
              >查看详情</el-button
            >
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  announcements: {
    type: Array,
    required: true,
  },
  timeFields: {
    type: Array,
    required: true,
    // 预期格式: [{ key: 'openTime', label: '开标时间' }, { key: 'closeTime', label: '截止时间' }]
  },
  showStatus: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['more-click', 'detail-click'])

// 计算状态列的偏移量
const statusOffset = computed(() => {
  return props.showStatus ? 0 : 3
})

const handleMoreClick = () => {
  emit('more-click')
}

const handleDetailClick = (item) => {
  emit('detail-click', item)
}
</script>

<style lang="scss" scoped>
.announcement-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dadada;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.more-link {
  font-size: 14px;
}

.announcement-list {
  .announcement-item {
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }
}

.announcement-title {
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
}

.time-info,
.status-info {
  .time-label,
  .status-label {
    font-size: 12px;
    color: #b3b3b3;
    margin-bottom: 4px;
  }

  .time-value {
    font-size: 12px;
    color: #b3b3b3;
  }
}
</style>

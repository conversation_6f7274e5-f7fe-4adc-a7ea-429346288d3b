<template>
  <header class="custom-header">
    <div class="header-container">
      <!-- Logo区域 -->
      <div class="logo-section">
        <router-link to="/" class="logo-link">
          <img src="@/assets/images/logo.svg" alt="Logo" class="logo-img" />
        </router-link>
      </div>

      <!-- 导航菜单区域 -->
      <nav class="nav-section">
        <ul class="nav-list">
          <li
            v-for="item in menuState"
            :key="item.key"
            class="nav-item"
            :class="{ 'has-dropdown': item.children }"
            @mouseenter="handleMouseEnter(item)"
            @mouseleave="handleMouseLeave(item)"
          >
            <router-link
              v-if="!item.children"
              :to="item.path"
              class="nav-link"
              :class="{ active: isActive(item.path) }"
            >
              {{ item.label }}
            </router-link>

            <div
              v-else
              class="nav-link dropdown-trigger"
              :class="{ active: isActiveDropdown(item) }"
            >
              {{ item.label }}
              <svg class="dropdown-icon" viewBox="0 0 1024 1024">
                <path
                  d="M831.872 340.864L512 652.672 192.128 340.864a30.592 30.592 0 00-42.752 0 29.12 29.12 0 000 41.6L489.6 714.24a30.592 30.592 0 0042.752 0l340.224-331.712a29.12 29.12 0 000-41.6 30.592 30.592 0 00-42.752 0z"
                />
              </svg>
            </div>

            <!-- 二级下拉菜单 -->
            <div
              v-if="item.children"
              class="dropdown-menu"
              :class="{ show: item.showDropdown }"
            >
              <router-link
                v-for="child in item.children"
                :key="child.key"
                :to="child.path"
                class="dropdown-item"
                :class="{ active: isActive(child.path) }"
              >
                {{ child.label }}
              </router-link>
            </div>
          </li>
        </ul>
      </nav>

      <!-- 右侧操作区域：搜索框+用户操作 -->
      <div class="right-section">
        <SearchBar class="header-search" />
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/store/modules/auth'
import SearchBar from '@/components/common/SearchBar.vue'

defineOptions({ name: 'YdHeader' })

const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const showUserMenu = ref(false)

// 响应式菜单状态
const menuState = reactive([])

// 计算属性
const userRole = computed(() => authStore.userRole)

// 菜单配置
const menuConfig = {
  visitor: [
    { key: 'home', label: '首页', path: '/' },
    {
      key: 'announcements',
      label: '竞价采购',
      children: [
        {
          key: 'bid-announcement',
          label: '竞价公告',
          path: '/bid-announcement',
        },
        {
          key: 'change-announcement',
          label: '变更公告',
          path: '/change-announcement',
        },
        {
          key: 'result-announcement',
          label: '结果公告',
          path: '/result-announcement',
        },
      ],
    },
    {
      key: 'help-center',
      label: '帮助中心',
      path: '/help-center',
    },
    {
      key: 'contact-us',
      label: '联系我们',
      path: '/contact-us',
    },
  ],
}

// 监听角色变化，深拷贝菜单并初始化showDropdown
watch(
  () => userRole.value,
  (role) => {
    menuState.length = 0
    const deepCopy = (arr) =>
      arr.map((item) => {
        const copy = { ...item }
        if (copy.children) {
          copy.children = deepCopy(copy.children)
          copy.showDropdown = false
        }
        return copy
      })
    const items = menuConfig[role || 'visitor'] || menuConfig.visitor
    deepCopy(items).forEach((i) => menuState.push(i))
  },
  { immediate: true },
)

// 方法
const isActive = (path) => {
  return route.path === path
}

const isActiveDropdown = (item) => {
  if (!item.children) return false
  return item.children.some((child) => route.path === child.path)
}

const handleMouseEnter = (item) => {
  if (item.children) {
    item.showDropdown = true
  }
}

const handleMouseLeave = (item) => {
  if (item.children) {
    item.showDropdown = false
  }
}

// 点击外部关闭用户菜单
const handleClickOutside = (event) => {
  if (!event.target.closest('.user-dropdown')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.custom-header {
  background-color: #001529;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  height: 64px;
}

/* Logo区域 */
.logo-section {
  margin-right: auto;
}

.logo-link {
  display: block;
  height: 40px;
}

.logo-img {
  height: 100%;
  width: auto;
}

/* 导航区域 */
.nav-section {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  margin-left: 3%;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  color: #fff;
  text-decoration: none;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 64px;
  box-sizing: border-box;
}

.nav-link:hover,
.nav-link.active {
  background-color: #555555;
  color: #fff;
}

.dropdown-trigger {
  position: relative;
}

.dropdown-icon {
  width: 12px;
  height: 12px;
  margin-left: 8px;
  fill: currentColor;
  transition: transform 0.3s ease;
}

.has-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 下拉菜单优化 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 100%;
  width: max-content;
  background-color: #001529;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1001;
  overflow: hidden;
  font-size: 15px;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 16px 24px;
  color: #666666;
  text-decoration: none;
  font-size: 15px;
  transition:
    background-color 0.2s,
    color 0.2s;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: #001529;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover,
.dropdown-item.active {
  background-color: #1890ff;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08);
}

/* 用户操作区域 */
.user-section {
  margin-left: auto;
  position: relative;
}

.auth-buttons {
  display: flex;
  gap: 12px;
}

.auth-btn {
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-btn {
  color: #fff;
  border: 1px solid #fff;
}

.login-btn:hover {
  background-color: #fff;
  color: #001529;
}

.register-btn {
  background-color: #1890ff;
  color: #fff;
  border: 1px solid #1890ff;
}

.register-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.user-dropdown {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: #fff;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.user-dropdown:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.username {
  margin-right: 8px;
  font-size: 14px;
}

.user-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 140px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-top: 8px;
}

.user-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-menu-item {
  display: block;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.user-menu-item:hover {
  background-color: #f5f5f5;
}

.user-menu-item.logout {
  color: #ff4d4f;
}

.user-menu-item.logout:hover {
  background-color: #fff2f0;
}

.user-menu-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 4px 0;
}

/* 右侧操作区域 */
.right-section {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-left: 24px;
}

.header-search {
  margin-right: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }

  .nav-link {
    padding: 20px 16px;
  }

  .dropdown-menu {
    min-width: 140px;
  }

  .auth-buttons {
    gap: 8px;
  }

  .auth-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .nav-link {
    padding: 20px 12px;
    font-size: 13px;
  }

  .dropdown-menu {
    min-width: 120px;
  }
}
</style>

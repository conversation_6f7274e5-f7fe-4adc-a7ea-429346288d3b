<template>
  <el-dialog
    v-model="dialogVisible"
    width="400px"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    align-center
    :before-close="handleClose"
  >
    <template #header>
      <div class="dialog-header">
        <span class="dialog-title">注册成功</span>
      </div>
    </template>

    <div class="dialog-content">
      <div class="success-message">
        <div class="message-text">您已提交注册信息，请等待审批！</div>
        <div class="sub-message">审批结果请注意查收邮件。</div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const dialogVisible = ref(false)

watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal
  },
)

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleConfirm = () => {
  emit('confirm')
  dialogVisible.value = false
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 0 20px;
    text-align: left;

    .dialog-header {
      .dialog-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }

  .el-dialog__body {
    padding: 20px;
    text-align: center;
  }

  .el-dialog__footer {
    padding: 0 20px 20px 20px;
    text-align: center;
  }
}

.dialog-content {
  .success-message {
    .message-text {
      font-size: 16px;
      color: #f56c6c;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .sub-message {
      font-size: 14px;
      color: #f56c6c;
      line-height: 1.5;
    }
  }
}

.dialog-footer {
  .el-button {
    padding: 8px 30px;
    font-size: 14px;
  }
}
</style>

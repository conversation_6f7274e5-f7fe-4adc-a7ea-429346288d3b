import { defineStore } from 'pinia'
import { getBidAnnouncements } from '@/api/announcement'

export const useAnnouncementStore = defineStore('announcement', {
  state: () => ({
    bidList: [],
    changeList: [],
    resultList: [],
    currentBid: null,
  }),
  actions: {
    async fetchBidAnnouncements(params) {
      const { data } = await getBidAnnouncements(params)
      this.bidList = data
    },
    async createBidAnnouncement(/*formData*/) {
      // 创建竞价公告逻辑
    },
    // 其他公告相关操作...
  },
})

import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isAuthenticated: false,
    userInfo: {
      id: '',
      name: '',
      email: '',
      phone: '',
      role: 'visitor', // visitor, supplier, purchaser, admin
      avatar: '',
      company: '',
      status: 'active', // active, inactive, pending
    },
    token: localStorage.getItem('token') || '',
    permissions: [],
  }),

  getters: {
    userRole: (state) => state.userInfo.role,
    isAdmin: (state) => state.userInfo.role === 'admin',
    isSupplier: (state) => state.userInfo.role === 'supplier',
    isPurchaser: (state) => state.userInfo.role === 'purchaser',
    isVisitor: (state) => state.userInfo.role === 'visitor',

    // 检查用户是否有特定权限
    hasPermission: (state) => (permission) => {
      return state.permissions.includes(permission)
    },
  },

  actions: {
    // 初始化认证状态
    initAuth() {
      const token = localStorage.getItem('token')
      const userInfo = localStorage.getItem('userInfo')

      if (token && userInfo) {
        this.token = token
        this.userInfo = JSON.parse(userInfo)
        this.isAuthenticated = true
        this.loadUserPermissions()
      }
    },

    // 用户登录
    async login(credentials) {
      try {
        // 这里应该调用实际的登录API
        // const response = await authAPI.login(credentials)

        // 模拟登录响应
        const mockResponse = {
          token: 'mock-token-' + Date.now(),
          user: {
            id: '1',
            name: credentials.username || '测试用户',
            email: credentials.email || '<EMAIL>',
            phone: '13800138000',
            role: credentials.role || 'supplier',
            avatar: '',
            company: '测试公司',
            status: 'active',
          },
          permissions: this.getRolePermissions(credentials.role || 'supplier'),
        }

        this.token = mockResponse.token
        this.userInfo = mockResponse.user
        this.permissions = mockResponse.permissions
        this.isAuthenticated = true

        // 保存到本地存储
        localStorage.setItem('token', this.token)
        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        localStorage.setItem('permissions', JSON.stringify(this.permissions))

        return { success: true, data: mockResponse }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },

    // 用户登出
    async logout() {
      try {
        // 这里应该调用实际的登出API
        // await authAPI.logout()

        this.token = ''
        this.userInfo = {
          id: '',
          name: '',
          email: '',
          phone: '',
          role: 'visitor',
          avatar: '',
          company: '',
          status: 'active',
        }
        this.permissions = []
        this.isAuthenticated = false

        // 清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        localStorage.removeItem('permissions')

        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },

    // 用户注册
    async register(registrationData) {
      try {
        // 这里应该调用实际的注册API
        // const response = await authAPI.register(registrationData)

        // 模拟注册响应
        const mockResponse = {
          success: true,
          message: '注册成功，请等待审核',
          user: {
            id: Date.now().toString(),
            name: registrationData.companyName,
            email: registrationData.email,
            phone: registrationData.phone,
            role: 'supplier',
            avatar: '',
            company: registrationData.companyName,
            status: 'pending',
          },
        }

        return mockResponse
      } catch (error) {
        return { success: false, error: error.message }
      }
    },

    // 更新用户信息
    async updateUserInfo(userInfo) {
      try {
        // 这里应该调用实际的更新API
        // const response = await authAPI.updateUserInfo(userInfo)

        this.userInfo = { ...this.userInfo, ...userInfo }
        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))

        return { success: true, data: this.userInfo }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        // 这里应该调用实际的修改密码API
        // const response = await authAPI.changePassword(passwordData)

        return { success: true, message: '密码修改成功' }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },

    // 加载用户权限
    loadUserPermissions() {
      const savedPermissions = localStorage.getItem('permissions')
      if (savedPermissions) {
        this.permissions = JSON.parse(savedPermissions)
      } else {
        this.permissions = this.getRolePermissions(this.userInfo.role)
      }
    },

    // 根据角色获取权限
    getRolePermissions(role) {
      const rolePermissions = {
        visitor: ['view:announcements', 'view:announcement:detail'],
        supplier: [
          'view:announcements',
          'view:announcement:detail',
          'participate:bid',
          'view:bid:history',
          'upload:qualification:documents',
          'view:qualification:status',
        ],
        purchaser: [
          'view:announcements',
          'view:announcement:detail',
          'create:bid:announcement',
          'edit:bid:announcement',
          'create:change:announcement',
          'create:result:announcement',
          'manage:bid:process',
          'view:bid:participants',
          'evaluate:bids',
        ],
        admin: [
          'view:announcements',
          'view:announcement:detail',
          'create:bid:announcement',
          'edit:bid:announcement',
          'create:change:announcement',
          'create:result:announcement',
          'manage:bid:process',
          'view:bid:participants',
          'evaluate:bids',
          'approve:suppliers',
          'manage:users',
          'manage:system:config',
          'view:system:logs',
        ],
      }

      return rolePermissions[role] || rolePermissions.visitor
    },

    // 检查路由权限
    checkRoutePermission(route) {
      if (!route.meta?.requiresAuth) {
        return true
      }

      if (!this.isAuthenticated) {
        return false
      }

      if (route.meta.role && this.userInfo.role !== route.meta.role) {
        return false
      }

      if (route.meta.permission && !this.hasPermission(route.meta.permission)) {
        return false
      }

      return true
    },

    // 刷新token
    async refreshToken() {
      try {
        // 这里应该调用实际的刷新token API
        // const response = await authAPI.refreshToken()

        const newToken = 'refreshed-token-' + Date.now()
        this.token = newToken
        localStorage.setItem('token', newToken)

        return { success: true, token: newToken }
      } catch (error) {
        // 刷新失败，清除认证信息
        await this.logout()
        return { success: false, error: error.message }
      }
    },
  },
})

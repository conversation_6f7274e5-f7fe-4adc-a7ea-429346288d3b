<template>
  <div class="flex items-center">
    <el-input
      v-model="searchKeyword"
      placeholder="请输入搜索关键词"
      :suffix-icon="Search"
      class="w-64"
      clearable
      @keyup.enter="handleSearch"
      @clear="handleClear"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'

const router = useRouter()
const searchKeyword = ref('')

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      name: 'SearchResults',
      query: { keyword: searchKeyword.value.trim() },
    })
  }
}

const handleClear = () => {
  searchKeyword.value = ''
}
</script>

<style scoped>
.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow: hidden;
  width: 250px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 8px 12px;
  background: transparent;
  font-size: 14px;
}

.search-button {
  background: #409eff;
  border: none;
  color: white;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  background: #337ecc;
}
</style>

<template>
  <el-card class="announcement-section">
    <template #header>
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
        <el-link
          :href="moreLink"
          type="primary"
          :underline="false"
          class="text-sm"
        >
          更多
        </el-link>
      </div>
    </template>

    <div class="space-y-3">
      <div
        v-for="announcement in announcements"
        :key="announcement.id"
        class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
      >
        <!-- 左侧公告信息 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2 mb-1">
            <h4 class="text-sm font-medium text-gray-900 truncate">
              {{ announcement.title }}
            </h4>
            <span
              v-if="announcement.code"
              class="text-xs text-gray-500 flex-shrink-0"
            >
              {{ announcement.code }}
            </span>
          </div>

          <div class="flex items-center space-x-4 text-xs text-gray-500">
            <span>发布时间：{{ announcement.publishTime }}</span>
            <span v-if="announcement.deadline">
              截止时间：{{ announcement.deadline }}
            </span>
          </div>
        </div>

        <!-- 右侧状态和操作 -->
        <div class="flex items-center space-x-3 flex-shrink-0">
          <el-tag :type="announcement.statusType" size="small" effect="plain">
            {{ announcement.status }}
          </el-tag>

          <el-link type="primary" :underline="false" class="text-sm">
            查看详情
          </el-link>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true,
  },
  announcements: {
    type: Array,
    default: () => [],
  },
  moreLink: {
    type: String,
    default: '#',
  },
})
</script>

<style scoped>
.announcement-section :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.announcement-section :deep(.el-card__body) {
  padding: 20px;
}
</style>

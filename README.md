# 招标采购平台 (MCP-BID)

## 项目简介

这是一个基于Vue 3 + Vite构建的现代化招标采购平台，支持多角色用户（访客、供应商、采购方、管理员）的不同功能需求。

## 主要功能

### 🎯 核心功能
- **公告管理**：竞价公告、变更公告、结果公告的发布与查看
- **用户权限**：基于角色的权限控制系统
- **竞价流程**：完整的竞价参与和管理流程
- **供应商管理**：供应商注册、审核、资格管理
- **系统配置**：灵活的系统参数配置

### 👥 用户角色
- **访客**：浏览公告、查看详情
- **供应商**：参与竞价、查看历史、资格审核
- **采购方**：发布公告、管理竞价、评估结果
- **管理员**：系统配置、用户审批、权限管理

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- pnpm >= 7.0.0

### 安装依赖
```bash
pnpm install
```

### 开发环境启动
```bash
pnpm dev
```

应用将在 [http://localhost:3000](http://localhost:3000) 启动

### 构建生产版本
```bash
pnpm build
```

### 预览生产版本
```bash
pnpm preview
```

## 📋 导航栏与下拉菜单交互规范

### 设计与交互规范
- **主色调**：`#001529` 深色背景，白色文字
- **响应式设计**：适配不同屏幕尺寸
- **权限控制**：根据用户角色动态显示菜单项
- **下拉菜单规范**：
  - 整体背景色：`#001529`
  - 未选中项文字颜色：`#666666`
  - 悬浮项和选中项：
    - 背景色：`#1890ff`
    - 文字颜色：`#fff`
    - 字体加粗
  - 选中项优先高亮（即当前路由匹配的菜单项）
  - 宽度与主菜单项对齐，圆角、阴影、过渡动画统一
  - 搜索框集成在Header右侧，和登录/注册按钮并列

### 技术实现
- **自定义组件**：摒弃Element Plus的el-menu，完全自定义实现
- **Vue 3 Composition API**：使用最新的Vue 3语法
- **Pinia状态管理**：集中管理用户认证状态
- **Vue Router**：完整的路由权限控制
- **菜单项使用响应式状态管理，支持动态角色切换**
- **只用CSS实现高亮和悬浮，无需JS hack**
- 相关样式已在`src/components/layout/Header.vue`中详细注释

### 导航栏菜单配置示例
```javascript
const menuConfig = {
  visitor: [
    { key: 'home', label: '首页', path: '/' },
    {
      key: 'announcements',
      label: '公告中心',
      children: [
        { key: 'bid-announcement', label: '竞价公告', path: '/bid-announcement' },
        { key: 'change-announcement', label: '变更公告', path: '/change-announcement' },
        { key: 'result-announcement', label: '结果公告', path: '/result-announcement' }
      ]
    }
  ]
  // ... 其他角色配置
}
```

### 下拉菜单样式片段
```css
.dropdown-menu {
  background-color: #001529;
}
.dropdown-item {
  color: #666666;
  background: #001529;
}
.dropdown-item:hover,
.dropdown-item.active {
  background-color: #1890ff;
  color: #fff;
  font-weight: 600;
}
```

### 权限控制
导航栏会根据用户认证状态和角色自动显示/隐藏菜单项：

```javascript
const visibleMenuItems = computed(() => {
  const role = userRole.value || 'visitor'
  return menuConfig[role] || menuConfig.visitor
})
```

## 🔐 认证系统

### 用户角色权限
- **visitor**：查看公告
- **supplier**：参与竞价、查看历史
- **purchaser**：管理竞价、发布公告
- **admin**：系统管理、用户审批

### 登录状态管理
使用Pinia进行状态管理，支持：
- 自动登录状态恢复
- Token刷新机制
- 权限验证
- 路由守卫

## 🛠️ 项目结构

```
src/
├── assets/                  # 静态资源
│   ├── images/              # 图片资源
│   └── styles/              # 全局样式
├── components/              # 公共组件
│   ├── layout/              # 布局组件
│   │   ├── Header.vue       # 自定义导航栏 ⭐
│   │   ├── Footer.vue       # 页脚组件
│   │   └── AppLayout.vue    # 主布局
│   └── common/              # 通用组件
├── router/                  # 路由配置
│   └── index.js             # 路由入口 + 权限守卫
├── store/                   # 状态管理
│   └── modules/
│       ├── auth.js          # 认证模块 ⭐
│       ├── announcement.js  # 公告管理
│       └── bid.js           # 竞价管理
├── utils/                   # 工具函数
├── views/                   # 页面视图
│   ├── visitor/             # 访客页面
│   ├── auth/                # 认证页面
│   ├── supplier/            # 供应商页面
│   ├── purchaser/           # 采购方页面
│   ├── admin/               # 管理员页面
│   ├── common/              # 通用页面
│   └── error/               # 错误页面
└── main.js                  # 应用入口
```

## 📝 开发指南

### 添加新菜单项
1. 在 `src/components/layout/Header.vue` 中的 `menuConfig` 添加配置
2. 在 `src/router/index.js` 中添加对应路由
3. 创建对应的页面组件

### 权限控制
1. 在路由meta中添加权限要求：
```javascript
{
  path: '/admin/config',
  meta: { requiresAuth: true, role: 'admin' }
}
```

2. 在组件中检查权限：
```javascript
const authStore = useAuthStore()
if (authStore.hasPermission('manage:system')) {
  // 显示管理功能
}
```

## 🎯 下一步计划

- [ ] 完善各页面的具体功能实现
- [ ] 添加更多的权限控制细节
- [ ] 优化移动端适配
- [ ] 添加国际化支持
- [ ] 完善错误处理和用户反馈
- [ ] 添加单元测试

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 问题反馈：[GitHub Issues](https://github.com/your-repo/mcp-bid/issues)

---

**注意**：当前版本为开发版本，部分功能仍在完善中。生产环境使用请确保充分测试。

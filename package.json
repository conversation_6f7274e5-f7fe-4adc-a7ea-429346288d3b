{"name": "mcp_bid", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --open", "preview": "rsbuild preview", "lint": "eslint", "lint-fix": "eslint --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.0", "dayjs": "^1.11.10", "element-plus": "^2.10.3", "lodash-es": "^4.5.0", "pinia": "^2.1.7", "vue": "^3.5.17", "vue-router": "^4.3.0", "xlsx": "^0.17.5"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@eslint/js": "^9.30.1", "@rsbuild/core": "^1.4.2", "@rsbuild/plugin-sass": "^1.3.3", "@rsbuild/plugin-vue": "^1.0.7", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "prettier": "^3.6.2", "sass": "^1.76.0", "typescript-eslint": "^8.36.0", "unocss": "^0.61.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vue-eslint-parser": "^10.2.0"}}
{"trailingComma": "all", "singleQuote": true, "semi": false, "printWidth": 80, "arrowParens": "always", "proseWrap": "always", "endOfLine": "auto", "experimentalTernaries": false, "tabWidth": 2, "useTabs": false, "quoteProps": "consistent", "jsxSingleQuote": false, "bracketSpacing": true, "bracketSameLine": false, "jsxBracketSameLine": false, "vueIndentScriptAndStyle": false, "singleAttributePerLine": false}
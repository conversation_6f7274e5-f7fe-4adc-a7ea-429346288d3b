<template>
  <div class="app-layout">
    <YdHeader />
    <main class="flex-1 bg-gray-50 main-content">
      <router-view />
    </main>
    <!-- <AppFooter /> -->
  </div>
</template>

<script setup>
import YdHeader from './Header.vue'
// import AppFooter from './Footer.vue'

defineOptions({
  name: 'AppLayout',
})
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 20px 1%;
}
</style>

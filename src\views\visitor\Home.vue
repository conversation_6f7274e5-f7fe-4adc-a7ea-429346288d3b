<template>
  <el-row>
    <el-col :span="15">
      <el-carousel height="200px" motion-blur>
        <el-carousel-item v-for="item in 4" :key="item">
          <h3 class="small justify-center" text="2xl">{{ item }}</h3>
        </el-carousel-item>
      </el-carousel>
    </el-col>
    <el-col :span="7" :offset="1">
      <div class="login-container">
        <h3 class="login-title">用户登录</h3>
        <el-form :model="loginForm" label-position="top" class="login-form">
          <el-form-item>
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              size="large"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              size="large"
              show-password
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
          <div class="register-link">
            <el-link type="primary" @click="handleRegister">供应商注册</el-link>
          </div>
        </el-form>
      </div>
    </el-col>
  </el-row>

  <el-card class="announcement-card">
    <AnnouncementBlock
      v-for="section in announcementSections"
      :key="section.key"
      :title="section.title"
      :announcements="section.announcements"
      :time-fields="section.timeFields"
      :show-status="section.showStatus"
      @more-click="handleMoreClick(section.key)"
      @detail-click="handleDetailClick"
    />
    <YdFooter />
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import YdFooter from '@/components/layout/Footer.vue'
import AnnouncementBlock from '@/components/common/AnnouncementBlock.vue'

defineOptions({
  name: 'HomePage',
})

const router = useRouter()

// 登录表单数据
const loginForm = ref({
  username: '',
  password: '',
})

// 招标公告数据
const bidAnnouncements = ref([
  {
    id: 1,
    title: 'XXX医院XXXX采购项目公告（JJGG202206094587）',
    openTime: '2022-12-01 12:00',
    closeTime: '2022-10-30 12:00',
    status: '已发布者',
  },
  {
    id: 2,
    title: 'XXX医院XXXX物品采购公告（JJGG202206094587）',
    openTime: '2022-10-01 12:00',
    closeTime: '2022-10-30 12:00',
    status: '已发布者',
  },
  {
    id: 3,
    title: 'XXX医院XXXX物品采购公告（JJGG202206094587）',
    openTime: '2022-09-01 12:00',
    closeTime: '2022-09-30 12:00',
    status: '已截止',
  },
])

// 变更公告数据
const changeAnnouncements = ref([
  {
    id: 1,
    title: 'XXX医院XXXX采购项目变更公告',
    publishTime: '2022-11-01 12:00',
    closeTime: '2022-10-30 12:00',
    status: '已发布者',
  },
  {
    id: 2,
    title: 'XXX医院XXXX物品采购变更公告',
    publishTime: '2022-10-01 12:00',
    closeTime: '2022-10-30 12:00',
    status: '已暂停',
  },
  {
    id: 3,
    title: 'XXX医院XXXX物品采购公告',
    publishTime: '2022-09-01 12:00',
    closeTime: '2022-09-30 12:00',
    status: '已截止',
  },
])

// 结果公告数据
const resultAnnouncements = ref([
  {
    id: 1,
    title: 'XXX医院XXXXXX采购项目结果公告',
    publishTime: '2022-10-30 12:00',
  },
  {
    id: 2,
    title: 'XXX医院XXXX物品采购结果公告',
    publishTime: '2022-09-30 12:00',
  },
])

// 公告配置数据
const announcementSections = ref([
  {
    key: 'bid',
    title: '招标公告',
    announcements: bidAnnouncements.value,
    timeFields: [
      { key: 'openTime', label: '开标时间' },
      { key: 'closeTime', label: '截止时间' },
    ],
    showStatus: true,
  },
  {
    key: 'change',
    title: '变更公告',
    announcements: changeAnnouncements.value,
    timeFields: [
      { key: 'publishTime', label: '发布时间' },
      { key: 'closeTime', label: '截止时间' },
    ],
    showStatus: true,
  },
  {
    key: 'result',
    title: '结果公告',
    announcements: resultAnnouncements.value,
    timeFields: [
      null, // 空占位符，对应第一个时间字段位置
      { key: 'publishTime', label: '发布时间' }, // 对应第二个时间字段位置
    ],
    showStatus: false,
  },
])

// 处理更多按钮点击
const handleMoreClick = (sectionKey) => {
  // 根据不同的公告类型处理跳转
  // 这里可以根据sectionKey进行路由跳转
  ElMessage.info(`点击了${sectionKey}的更多按钮`)
}

// 处理详情按钮点击
const handleDetailClick = (item) => {
  // 这里可以跳转到详情页面
  ElMessage.info(`查看${item.title}的详情`)
}

// 处理登录
const handleLogin = () => {
  if (!loginForm.value.username || !loginForm.value.password) {
    ElMessage.warning('请输入用户名和密码')
    return
  }
  // 这里可以添加登录逻辑
  ElMessage.success('登录功能待实现')
}

// 处理供应商注册跳转
const handleRegister = () => {
  router.push({ name: 'SupplierRegister' })
}
</script>

<style lang="scss" coped>
$test-color: #42b983;
.test-scss {
  color: $test-color;
  font-weight: bold;
}
.login-container {
  padding: 24px;
  border-radius: 8px;
  margin-left: 16px;

  .login-title {
    font-size: 18px;
    font-weight: 500;
    text-align: left;
    color: #409eff;
    position: relative;
    margin-bottom: 32px;
    display: inline-block;
  }
  .login-title::after {
    content: '';
    display: block;
    width: 120%;
    height: 3px;
    background: #409eff;
    margin: 10px auto 0 0;
    border-radius: 2px;
  }
  .login-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .login-button {
      width: 100%;
    }

    .register-link {
      text-align: right;
      margin-top: 16px;

      .el-link {
        font-size: 14px;
      }
    }
  }
}

.announcement-card {
  margin-top: 24px;
}
</style>
